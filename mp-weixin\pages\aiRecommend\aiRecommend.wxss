/* AI推荐页面样式 */
.ai-recommend-container {
  min-height: 100vh;
  background: #F5F5F5;
  position: relative;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: calc(60rpx + env(safe-area-inset-top));
  background: #EE5359;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  min-height: 120rpx;
  box-shadow: 0 4rpx 20rpx rgba(238, 83, 89, 0.15);
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.nav-back:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
}

.nav-placeholder {
  width: 60rpx;
}

/* 主要内容 */
.content {
  padding: 20rpx 30rpx;
  padding-top: calc(140rpx + env(safe-area-inset-top) + 40rpx);
  padding-bottom: 100rpx;
  margin-top: 40rpx;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(238, 83, 89, 0.1);
}

.input-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.input-subtitle {
  font-size: 28rpx;
  font-weight: 400;
  color: #888;
  margin-bottom: 40rpx;
}

.input-container {
  position: relative;
  margin-bottom: 40rpx;
}

.input-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 400;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
}

.input-textarea:focus {
  border-color: #EE5359;
  box-shadow: 0 0 0 4rpx rgba(238, 83, 89, 0.1), inset 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
}

.input-counter {
  position: absolute;
  bottom: 16rpx;
  right: 24rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #bbb;
}

/* 快捷标签 */
.quick-tags {
  margin-bottom: 40rpx;
}

.tags-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #888;
  margin-bottom: 20rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #888;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(238, 83, 89, 0.1);
}

.tag-item.selected {
  background: #EE5359;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(238, 83, 89, 0.25);
  transform: translateY(-1rpx);
}

/* 推荐按钮 */
.recommend-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  background: #e0e0e0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.recommend-btn.active {
  background: #EE5359;
  box-shadow: 0 6rpx 20rpx rgba(238, 83, 89, 0.3), 0 2rpx 8rpx rgba(238, 83, 89, 0.15);
  transform: translateY(-1rpx);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

/* 加载状态 */
.loading-section {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  animation: loading-fade-in 0.3s ease-out;
  border: 1rpx solid rgba(238, 83, 89, 0.1);
}

.loading-card {
  padding: 40rpx;
  text-align: center;
  background: linear-gradient(135deg, #fef7f7 0%, #ffffff 100%);
}

.loading-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}

.ai-thinking-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  animation: thinking-bounce 2s ease-in-out infinite;
}

.loading-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.loading-content {
  margin-bottom: 32rpx;
}

.loading-dots-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
  height: 40rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  background: #EE5359;
  border-radius: 50%;
  margin: 0 6rpx;
  opacity: 0.3;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.loading-subtitle {
  font-size: 26rpx;
  font-weight: 400;
  color: #888;
  line-height: 1.4;
}

.loading-progress {
  margin-top: 24rpx;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: #f0f0f0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #EE5359 0%, #ff7b7f 100%);
  border-radius: 3rpx;
  animation: progress-loading 2s ease-in-out infinite;
}

/* 推荐结果 */
.result-section {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(238, 83, 89, 0.1);
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx 24rpx 40rpx;
  background: #EE5359;
  position: relative;
}

.result-header-left {
  display: flex;
  align-items: center;
}

.result-header-right {
  display: flex;
  align-items: center;
}

.result-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.2);
}

.ai-avatar {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx;
  box-sizing: border-box;
}

.result-title {
  font-size: 32rpx;
  font-weight: 700;
  color: white;
}

.new-search-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.new-search-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

.refresh-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.new-search-text {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}

/* 菜品列表 */
.dish-list {
  padding: 20rpx 30rpx;
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
}

.dish-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(238, 83, 89, 0.08);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 200rpx;
}

.dish-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12), 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.dish-image-container {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
  align-self: center;
}

.dish-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  object-fit: cover;
  background-color: #f5f5f5;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.recommend-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #EE5359;
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
  box-shadow: 0 2rpx 8rpx rgba(238, 83, 89, 0.3);
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
  overflow: hidden;
}

.dish-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-description {
  font-size: 26rpx;
  font-weight: 400;
  color: #888;
  margin-bottom: 12rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.recommend-reason {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #fef7f7;
  border-radius: 8rpx;
  border-left: 4rpx solid #EE5359;
}

.reason-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.reason-text {
  font-size: 24rpx;
  font-weight: 400;
  color: #5DADE2;
  flex: 1;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.dish-price {
  display: flex;
  align-items: baseline;
  flex-shrink: 0;
}

.price-symbol {
  font-size: 24rpx;
  font-weight: 600;
  color: #EE5359;
  margin-right: 2rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #EE5359;
}

.dish-actions {
  margin-left: 16rpx;
}

.add-to-cart {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #EE5359;
  color: white;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
  white-space: nowrap;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(238, 83, 89, 0.25);
}

.add-to-cart:active {
  background: #d94449;
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(238, 83, 89, 0.3);
}

.cart-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 重新推荐 */
.retry-section {
  padding: 32rpx 40rpx 40rpx 40rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.retry-btn {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 40rpx;
  background: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #EE5359;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(238, 83, 89, 0.2);
}

.retry-btn:active {
  background: #fef7f7;
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.retry-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

/* 空状态 */
.empty-section {
  background: white;
  border-radius: 12rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(238, 83, 89, 0.1);
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  font-weight: 400;
  color: #888;
}

/* 小屏幕优化样式 - 微信小程序不支持@media，这些样式作为备用 */
.dish-item-small {
  padding: 20rpx 0;
}

.dish-image-small {
  width: 140rpx;
  height: 140rpx;
}

.dish-name-small {
  font-size: 30rpx;
}

.dish-description-small {
  font-size: 24rpx;
}

.reason-text-small {
  font-size: 22rpx;
}

.price-value-small {
  font-size: 32rpx;
}

.add-to-cart-small {
  padding: 10rpx 16rpx;
  font-size: 22rpx;
}

/* 确保文本不会溢出 */
.dish-name,
.dish-description,
.reason-text {
  word-wrap: break-word;
  word-break: break-all;
}

/* 统一间距 */
.dish-list .dish-item:first-child {
  padding-top: 32rpx;
}

.dish-list .dish-item:last-child {
  padding-bottom: 0;
}

/* 加载动画效果 - 使用微信小程序支持的简单动画 */
.loading-fade-in {
  animation: fade-in 0.3s ease-out;
}

.thinking-bounce {
  animation: bounce 2s ease-in-out infinite;
}

.progress-loading {
  animation: progress 2s ease-in-out infinite;
}

/* 简单的CSS动画 - 微信小程序支持 */
.loading-dot.active {
  opacity: 1;
  transform: scale(1.2);
  background: #d94449;
}

/* 渐变动画效果 */
.progress-fill {
  width: 0%;
  animation: progress-width 3s ease-in-out infinite;
}

/* 菜品详情弹窗样式 - 与主页保持一致 */
.pop_mask {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.4);
}

.dish_detail_pop {
  width: calc(100vw - 160rpx);
  box-sizing: border-box;
  position: relative;
  top: 50%;
  left: 50%;
  padding: 40rpx;
  -webkit-transform: translateX(-50%) translateY(-50%);
          transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
}

.dish_detail_pop .div_big_image {
  width: 100%;
  height: 320rpx;
  border-radius: 10rpx;
}

.dish_detail_pop .title {
  font-size: 40rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: 700;
  color: #1a1a1a;
}

.dish_detail_pop .desc {
  font-size: 28rpx;
  color: #888;
  text-align: center;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.dish_detail_pop .dish_items {
  height: 60vh;
}

.dish_detail_pop .but_item {
  display: flex;
  position: relative;
  flex: 1;
}

.dish_detail_pop .but_item .price {
  text-align: left;
  color: #e94e3c;
  line-height: 88rpx;
  box-sizing: border-box;
  font-size: 48rpx;
  font-weight: bold;
}

.dish_detail_pop .but_item .price .ico {
  font-size: 28rpx;
}

.dish_detail_pop .but_item .active {
  position: absolute;
  right: 0rpx;
  bottom: 20rpx;
  display: flex;
}

.dish_detail_pop .but_item .active .dish_add {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.dish_detail_pop .but_item .active .dish_red {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.dish_detail_pop .but_item .active .dish_number {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}

.dish_detail_pop .but_item .active .dish_card_add {
  width: 200rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 600;
  font-size: 28rpx;
  opacity: 1;
  background: #EE5359;
  color: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(238, 83, 89, 0.25);
}

.dish_detail_pop .dish_item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.dish_detail_pop .dish_item:last-child {
  border-bottom: none;
}

.dish_detail_pop .dish_item .div_big_image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.dish_detail_pop .dish_item .title {
  font-size: 32rpx;
  line-height: 1.4;
  text-align: left;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.dish_detail_pop .dish_item .desc {
  font-size: 24rpx;
  color: #888;
  text-align: left;
  margin-bottom: 0;
  line-height: 1.3;
}

.dish_detail_pop .dish_item .dish_info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.pop_mask .close {
  position: absolute;
  bottom: -180rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.pop_mask .close .close_img {
  width: 88rpx;
  height: 88rpx;
}

/* 规格选择弹窗样式 - 与主页保持一致 */
.more_norm_pop {
  width: calc(100vw - 160rpx);
  box-sizing: border-box;
  position: relative;
  top: 50%;
  left: 50%;
  padding: 40rpx;
  -webkit-transform: translateX(-50%) translateY(-50%);
          transform: translateX(-50%) translateY(-50%);
  background: #fff;
  border-radius: 20rpx;
}

.more_norm_pop .div_big_image {
  width: 100%;
  border-radius: 10rpx;
}

.more_norm_pop .title {
  font-size: 40rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: 700;
  color: #1a1a1a;
}

.more_norm_pop .items_cont {
  display: flex;
  flex-wrap: wrap;
  margin-left: -14rpx;
  max-height: 50vh;
}

.more_norm_pop .items_cont .item_row .flavor_name {
  height: 40rpx;
  opacity: 1;
  font-size: 28rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 600;
  text-align: left;
  color: #1a1a1a;
  line-height: 40rpx;
  padding-left: 10rpx;
  padding-top: 20rpx;
}

.more_norm_pop .items_cont .item_row .flavor_item {
  display: flex;
  flex-wrap: wrap;
}

.more_norm_pop .items_cont .item_row .flavor_item .item {
  border: 1px solid #ffb302;
  border-radius: 12rpx;
  margin: 20rpx 10rpx;
  padding: 0 26rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  color: #888;
  background: #ffffff; /* 明确设置白色背景 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}

/* 选中状态的样式 */
.more_norm_pop .items_cont .item_row .flavor_item .item.selected {
  background: #ffc200 !important;
  border-color: #ffc200 !important;
}

.more_norm_pop .items_cont .item_row .flavor_item .item.act,
.more_norm_pop .items_cont .item_row .flavor_item .act {
  background: #ffc200 !important;
  border: 1px solid #ffc200 !important;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 600;
  color: #1a1a1a;
}

/* 新的口味选择样式 - 使用更高优先级的选择器 */
.more_norm_pop .items_cont .item_row .flavor_item view.flavor-item {
  border: 1px solid #ffb302;
  border-radius: 12rpx;
  margin: 20rpx 10rpx;
  padding: 0 26rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-family: PingFangSC, PingFangSC-Regular;
  font-weight: 400;
  color: #888;
  background: #ffffff;
  transition: all 0.3s ease;
  display: inline-block;
}

.more_norm_pop .items_cont .item_row .flavor_item view.flavor-item.selected {
  background: #ffc200 !important;
  border: 1px solid #ffc200 !important;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 600;
  color: #1a1a1a;
}

.more_norm_pop .items_cont .item_row .flavor_item view.flavor-item.unselected {
  background: #ffffff !important;
  border: 1px solid #ffb302 !important;
}

.more_norm_pop .but_item {
  display: flex;
  position: relative;
  flex: 1;
  padding-left: 10rpx;
  margin: 34rpx 0 -20rpx 0;
}

.more_norm_pop .but_item .price {
  text-align: left;
  color: #e94e3c;
  line-height: 88rpx;
  box-sizing: border-box;
  font-size: 48rpx;
  font-family: DIN, DIN-Medium;
  font-weight: 500;
}

.more_norm_pop .but_item .price .ico {
  font-size: 28rpx;
}

.more_norm_pop .but_item .active {
  position: absolute;
  right: 0rpx;
  bottom: 20rpx;
  display: flex;
}

.more_norm_pop .but_item .active .dish_add {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.more_norm_pop .but_item .active .dish_red {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.more_norm_pop .but_item .active .dish_number {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
}

.more_norm_pop .but_item .active .dish_card_add {
  width: 200rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 600;
  font-size: 28rpx;
  opacity: 1;
  background: #EE5359;
  color: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(238, 83, 89, 0.25);
}

.dish_detail_pop .but_item .active {
  display: flex;
  align-items: center;
}

.dish_detail_pop .but_item .active .dish_add {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.dish_detail_pop .but_item .active .dish_red {
  display: block;
  width: 72rpx;
  height: 72rpx;
}

.dish_detail_pop .but_item .active .dish_number {
  padding: 0 10rpx;
  line-height: 72rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.dish_detail_pop .but_item .active .dish_card_add {
  width: 200rpx;
  line-height: 60rpx;
  text-align: center;
  font-weight: 600;
  font-size: 28rpx;
  background: #EE5359;
  border-radius: 12rpx;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(238, 83, 89, 0.25);
}

.dish_detail_pop .close {
  position: absolute;
  bottom: -180rpx;
  left: 50%;
  transform: translateX(-50%);
}

.dish_detail_pop .close .close_img {
  width: 88rpx;
  height: 88rpx;
}
